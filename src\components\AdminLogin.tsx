import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/hooks/use-toast';
import { Shield, Crown, Lock, Mail, Eye, EyeOff } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { adminAuthService } from '@/services/adminAuth';

const AdminLogin = () => {
  const { toast } = useToast();
  const navigate = useNavigate();
  const [isLoading, setIsLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [formData, setFormData] = useState({
    email: '<EMAIL>', // Pre-filled with admin email
    password: ''
  });

  const handleAdminSignIn = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      const { user, error } = await adminAuthService.signIn(formData.email, formData.password);

      if (error || !user) {
        toast({
          title: "Admin Sign In Failed",
          description: error || "Invalid credentials",
          variant: "destructive",
        });
      } else {
        toast({
          title: `Welcome ${user.full_name || 'Admin'}!`,
          description: "Redirecting to admin panel...",
        });

        // Immediate redirect to admin panel
        navigate('/admin', { replace: true });
      }
    } catch (error) {
      console.error('Admin login error:', error);
      toast({
        title: "Admin Sign In Failed",
        description: "An unexpected error occurred",
        variant: "destructive",
      });
    }

    setIsLoading(false);
  };

  const handleQuickLogin = () => {
    setFormData({
      email: '<EMAIL>',
      password: 'samsun@55'
    });
  };

  return (
    <Card className="border-2 border-red-200 bg-gradient-to-br from-red-50 to-orange-50">
      <CardHeader className="text-center">
        <div className="flex items-center justify-center gap-2 mb-2">
          <Shield className="h-6 w-6 text-red-600" />
          <Crown className="h-5 w-5 text-yellow-500" />
        </div>
        <CardTitle className="text-red-700 flex items-center justify-center gap-2">
          <Lock className="h-5 w-5" />
          Admin Access
        </CardTitle>
        <CardDescription className="text-red-600">
          Restricted access for administrators only
        </CardDescription>
        <div className="flex justify-center">
          <Badge variant="destructive" className="bg-red-600">
            Admin Panel Login
          </Badge>
        </div>
      </CardHeader>
      
      <form onSubmit={handleAdminSignIn}>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="admin-email" className="text-red-700 font-semibold">
              <Mail className="h-4 w-4 inline mr-1" />
              Admin Email
            </Label>
            <Input
              id="admin-email"
              type="email"
              value={formData.email}
              onChange={(e) => setFormData({ ...formData, email: e.target.value })}
              placeholder="<EMAIL>"
              className="border-red-200 focus:border-red-400"
              required
            />
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="admin-password" className="text-red-700 font-semibold">
              <Lock className="h-4 w-4 inline mr-1" />
              Admin Password
            </Label>
            <div className="relative">
              <Input
                id="admin-password"
                type={showPassword ? "text" : "password"}
                value={formData.password}
                onChange={(e) => setFormData({ ...formData, password: e.target.value })}
                placeholder="Enter admin password"
                className="border-red-200 focus:border-red-400 pr-10"
                required
              />
              <Button
                type="button"
                variant="ghost"
                size="sm"
                className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                onClick={() => setShowPassword(!showPassword)}
              >
                {showPassword ? (
                  <EyeOff className="h-4 w-4 text-gray-400" />
                ) : (
                  <Eye className="h-4 w-4 text-gray-400" />
                )}
              </Button>
            </div>
          </div>

          <div className="bg-red-50 border border-red-200 rounded-lg p-3">
            <h4 className="text-sm font-semibold text-red-700 mb-2">Quick Admin Login</h4>
            <p className="text-xs text-red-600 mb-2">
              Use the pre-configured admin credentials:
            </p>
            <div className="text-xs text-red-600 space-y-1 mb-3">
              <div><strong>Email:</strong> <EMAIL></div>
              <div><strong>Password:</strong> samsun@55</div>
            </div>
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={handleQuickLogin}
              className="w-full border-red-300 text-red-700 hover:bg-red-100"
            >
              Fill Admin Credentials
            </Button>
          </div>

          <Button
            type="submit"
            disabled={isLoading}
            className="w-full bg-red-600 hover:bg-red-700 text-white"
          >
            {isLoading ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                Signing In...
              </>
            ) : (
              <>
                <Shield className="h-4 w-4 mr-2" />
                Sign In as Admin
              </>
            )}
          </Button>

          <div className="text-center">
            <div className="text-xs text-red-600 bg-red-50 border border-red-200 rounded p-2">
              <strong>⚠️ Admin Only:</strong> This login is restricted to authorized administrators.
              Unauthorized access attempts are logged and monitored.
            </div>
          </div>
        </CardContent>
      </form>
    </Card>
  );
};

export default AdminLogin;
