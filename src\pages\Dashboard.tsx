import React, { useEffect, useState } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { Navigate, useNavigate, Link } from 'react-router-dom';
import { supabase } from '@/integrations/supabase/client';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { BookOpen, Clock, Award, TrendingUp, User, LogOut, Home, ArrowRight } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

interface Enrollment {
  id: string;
  progress: number;
  enrolled_at: string;
  completed_at: string | null;
  courses: {
    id: string;
    title: string;
    category: string;
    level: string;
    duration: string;
    image_url: string;
  };
}

interface Profile {
  id: string;
  full_name: string;
  email: string;
  country: string;
}

const Dashboard = () => {
  const { user, signOut, loading } = useAuth();
  const { toast } = useToast();
  const navigate = useNavigate();
  const [enrollments, setEnrollments] = useState<Enrollment[]>([]);
  const [profile, setProfile] = useState<Profile | null>(null);
  const [dashboardLoading, setDashboardLoading] = useState(true);

  // Redirect if not authenticated
  if (!user && !loading) {
    return <Navigate to="/" replace />;
  }

  useEffect(() => {
    if (user) {
      fetchDashboardData();
    }
  }, [user]);

  const fetchDashboardData = async () => {
    try {
      // Fetch user profile
      const { data: profileData } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', user?.id)
        .single();

      setProfile(profileData);

      // Fetch enrollments with course details
      const { data: enrollmentData } = await supabase
        .from('enrollments')
        .select(`
          *,
          courses (
            id,
            title,
            category,
            level,
            duration,
            image_url
          )
        `)
        .eq('user_id', user?.id)
        .order('enrolled_at', { ascending: false });

      setEnrollments(enrollmentData || []);
    } catch (error) {
      console.error('Error fetching dashboard data:', error);
      toast({
        title: "Error",
        description: "Failed to load dashboard data",
        variant: "destructive",
      });
    } finally {
      setDashboardLoading(false);
    }
  };

  const handleSignOut = async () => {
    const { error } = await signOut();
    if (error) {
      toast({
        title: "Error",
        description: "Failed to sign out",
        variant: "destructive",
      });
    } else {
      // Navigate to main page after successful sign out
      navigate('/');
      toast({
        title: "Signed Out",
        description: "You have been successfully signed out",
      });
    }
  };

  const getProgressColor = (progress: number) => {
    if (progress >= 80) return "bg-primary";
    if (progress >= 40) return "bg-warning";
    return "bg-secondary";
  };

  if (loading || dashboardLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-green-50 to-emerald-100 flex items-center justify-center">
        <div className="text-center">
          <img
            src="/lovable-uploads/4df5a54b-5bf0-4458-a8c5-d397a73b2876.png"
            alt="Wisdom Tree Academy"
            className="h-16 w-16 mx-auto mb-4 animate-pulse"
          />
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-green-600 mx-auto mb-4"></div>
          <p className="text-lg text-green-700 font-medium">Loading your dashboard...</p>
        </div>
      </div>
    );
  }

  const completedCourses = enrollments.filter(e => e.completed_at).length;
  const totalProgress = enrollments.length > 0 
    ? Math.round(enrollments.reduce((sum, e) => sum + e.progress, 0) / enrollments.length)
    : 0;

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 to-emerald-100">
      {/* Header */}
      <div className="bg-white/95 backdrop-blur-sm shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-3">
                <img
                  src="/lovable-uploads/4df5a54b-5bf0-4458-a8c5-d397a73b2876.png"
                  alt="Wisdom Tree Academy"
                  className="h-12 w-12 animate-fade-in"
                />
                <div>
                  <h1 className="text-3xl md:text-4xl font-bold text-gray-900 animate-fade-in">
                    Welcome back, {profile?.full_name || 'Student'}!
                  </h1>
                  <p className="text-lg text-green-700 animate-fade-in">
                    Continue your learning journey with Wisdom Tree Academy
                  </p>
                </div>
              </div>
            </div>
            <div className="flex items-center gap-3">
              <Button asChild variant="outline" size="lg" className="border-green-600 text-green-600 hover:bg-green-50">
                <Link to="/">
                  <Home className="h-4 w-4 mr-2" />
                  Main Page
                </Link>
              </Button>
              <Button onClick={handleSignOut} size="lg" className="bg-green-600 hover:bg-green-700 text-white">
                <LogOut className="h-4 w-4 mr-2" />
                Sign Out
              </Button>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Stats Overview */}
        <div className="mb-16">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4 animate-fade-in">
              Your Learning Progress
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto animate-fade-in">
              Track your achievements and continue building your skills
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <Card className="hover:shadow-lg transition-shadow border-0 shadow-md hover-scale bg-white">
              <CardContent className="p-6">
                <div className="flex items-center gap-4">
                  <BookOpen className="h-10 w-10 text-green-600" />
                  <div>
                    <p className="text-3xl font-bold text-gray-900">{enrollments.length}</p>
                    <p className="text-sm text-gray-600">Enrolled Courses</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="hover:shadow-lg transition-shadow border-0 shadow-md hover-scale bg-white">
              <CardContent className="p-6">
                <div className="flex items-center gap-4">
                  <Award className="h-10 w-10 text-green-600" />
                  <div>
                    <p className="text-3xl font-bold text-gray-900">{completedCourses}</p>
                    <p className="text-sm text-gray-600">Completed</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="hover:shadow-lg transition-shadow border-0 shadow-md hover-scale bg-white">
              <CardContent className="p-6">
                <div className="flex items-center gap-4">
                  <TrendingUp className="h-10 w-10 text-green-600" />
                  <div>
                    <p className="text-3xl font-bold text-gray-900">{totalProgress}%</p>
                    <p className="text-sm text-gray-600">Avg Progress</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="hover:shadow-lg transition-shadow border-0 shadow-md hover-scale bg-white">
              <CardContent className="p-6">
                <div className="flex items-center gap-4">
                  <Clock className="h-10 w-10 text-green-600" />
                  <div>
                    <p className="text-3xl font-bold text-gray-900">{enrollments.length - completedCourses}</p>
                    <p className="text-sm text-gray-600">In Progress</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Enrolled Courses */}
        <section className="py-12 bg-white rounded-2xl shadow-md">
          <div className="px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                My Courses
              </h2>
              <p className="text-lg text-gray-600 max-w-2xl mx-auto">
                Track your progress and continue your learning journey
              </p>
            </div>

            {enrollments.length === 0 ? (
              <div className="text-center py-16">
                <BookOpen className="h-16 w-16 text-green-600 mx-auto mb-6" />
                <h3 className="text-2xl font-bold text-gray-900 mb-4">No courses enrolled yet</h3>
                <p className="text-lg text-gray-600 mb-8 max-w-md mx-auto">
                  Start your learning journey by enrolling in a course and unlock your potential
                </p>
                <Button size="lg" asChild className="bg-green-600 hover:bg-green-700 text-white">
                  <Link to="/courses">
                    Browse Courses
                    <ArrowRight className="ml-2 h-5 w-5" />
                  </Link>
                </Button>
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                {enrollments.map((enrollment) => (
                  <Card key={enrollment.id} className="hover:shadow-lg transition-shadow border-0 shadow-md hover-scale bg-white overflow-hidden">
                    <div className="aspect-video bg-gradient-to-br from-green-50 to-emerald-100 flex items-center justify-center">
                      <BookOpen className="h-16 w-16 text-green-600" />
                    </div>
                    <CardContent className="p-6">
                      <div className="flex items-start justify-between mb-4">
                        <h3 className="text-xl font-semibold text-gray-900 leading-tight">
                          {enrollment.courses.title}
                        </h3>
                        {enrollment.completed_at && (
                          <Badge className="bg-green-100 text-green-600 border-green-200">
                            Completed
                          </Badge>
                        )}
                      </div>

                      <div className="flex items-center gap-2 mb-4">
                        <span className="text-sm font-medium text-green-600 bg-green-100 px-3 py-1 rounded-full">
                          {enrollment.courses.category}
                        </span>
                        <Badge variant="outline" className="text-gray-600">
                          {enrollment.courses.level}
                        </Badge>
                      </div>

                      <div className="space-y-3 mb-4">
                        <div className="flex items-center justify-between text-sm text-gray-600">
                          <span>Progress</span>
                          <span className="font-semibold text-gray-900">{enrollment.progress}%</span>
                        </div>
                        <Progress
                          value={enrollment.progress}
                          className="h-3"
                        />
                      </div>

                      <div className="flex items-center justify-between text-sm text-gray-500 pt-4 border-t border-gray-100">
                        <span>{enrollment.courses.duration}</span>
                        <span>
                          Enrolled {new Date(enrollment.enrolled_at).toLocaleDateString()}
                        </span>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}
          </div>
        </section>
      </div>
    </div>
  );
};

export default Dashboard;