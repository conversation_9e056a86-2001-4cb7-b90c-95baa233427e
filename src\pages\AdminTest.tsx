import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Link } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { useAdmin } from '@/contexts/AdminContext';
import { Shield, User, CheckCircle, XCircle, AlertTriangle } from 'lucide-react';

const AdminTest = () => {
  const { user } = useAuth();
  const { isAdmin, loading } = useAdmin();

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 to-emerald-100 p-8">
      <div className="max-w-4xl mx-auto">
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            Admin Panel Test Page
          </h1>
          <p className="text-lg text-gray-600">
            Debug and test admin panel access
          </p>
        </div>

        <div className="grid gap-6">
          {/* Authentication Status */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <User className="h-5 w-5" />
                Authentication Status
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <span>User Logged In:</span>
                  {user ? (
                    <Badge className="bg-green-100 text-green-700">
                      <CheckCircle className="h-3 w-3 mr-1" />
                      Yes
                    </Badge>
                  ) : (
                    <Badge variant="destructive">
                      <XCircle className="h-3 w-3 mr-1" />
                      No
                    </Badge>
                  )}
                </div>
                
                {user && (
                  <>
                    <div className="flex items-center justify-between">
                      <span>Email:</span>
                      <code className="bg-gray-100 px-2 py-1 rounded text-sm">
                        {user.email || 'No email'}
                      </code>
                    </div>
                    
                    <div className="flex items-center justify-between">
                      <span>User ID:</span>
                      <code className="bg-gray-100 px-2 py-1 rounded text-sm">
                        {user.id}
                      </code>
                    </div>
                  </>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Admin Status */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Shield className="h-5 w-5" />
                Admin Status
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <span>Admin Check Loading:</span>
                  {loading ? (
                    <Badge variant="secondary">
                      <AlertTriangle className="h-3 w-3 mr-1" />
                      Loading...
                    </Badge>
                  ) : (
                    <Badge className="bg-green-100 text-green-700">
                      <CheckCircle className="h-3 w-3 mr-1" />
                      Complete
                    </Badge>
                  )}
                </div>
                
                <div className="flex items-center justify-between">
                  <span>Is Admin:</span>
                  {isAdmin ? (
                    <Badge className="bg-green-100 text-green-700">
                      <CheckCircle className="h-3 w-3 mr-1" />
                      Yes - Admin Access Granted
                    </Badge>
                  ) : (
                    <Badge variant="destructive">
                      <XCircle className="h-3 w-3 mr-1" />
                      No - Admin Access Denied
                    </Badge>
                  )}
                </div>

                <div className="bg-gray-50 p-4 rounded-lg">
                  <h4 className="font-semibold mb-2">Admin Email List:</h4>
                  <ul className="text-sm space-y-1">
                    <li>• <EMAIL></li>
                    <li>• <EMAIL></li>
                    <li>• <EMAIL></li>
                    <li>• <EMAIL></li>
                  </ul>
                  <p className="text-xs text-gray-600 mt-2">
                    Your email must be in this list to access the admin panel
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Actions */}
          <Card>
            <CardHeader>
              <CardTitle>Actions</CardTitle>
              <CardDescription>
                Test admin panel access and navigation
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex flex-wrap gap-4">
                {!user && (
                  <Button asChild>
                    <Link to="/auth">
                      Sign In First
                    </Link>
                  </Button>
                )}
                
                {user && !isAdmin && (
                  <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 w-full">
                    <h4 className="font-semibold text-yellow-800 mb-2">
                      Admin Access Required
                    </h4>
                    <p className="text-yellow-700 text-sm mb-3">
                      Your email ({user.email}) is not in the admin list. 
                      To access the admin panel, you need to:
                    </p>
                    <ol className="text-yellow-700 text-sm space-y-1 ml-4">
                      <li>1. Sign in with an admin email, OR</li>
                      <li>2. Add your email to the admin list in the code</li>
                    </ol>
                  </div>
                )}
                
                {user && isAdmin && (
                  <Button asChild className="bg-green-600 hover:bg-green-700">
                    <Link to="/admin">
                      <Shield className="h-4 w-4 mr-2" />
                      Access Admin Panel
                    </Link>
                  </Button>
                )}
                
                <Button variant="outline" asChild>
                  <Link to="/">
                    Back to Home
                  </Link>
                </Button>
                
                <Button variant="outline" asChild>
                  <Link to="/dashboard">
                    User Dashboard
                  </Link>
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Instructions */}
          <Card>
            <CardHeader>
              <CardTitle>How to Access Admin Panel</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4 text-sm">
                <div>
                  <h4 className="font-semibold mb-2">Method 1: Use Test Email</h4>
                  <p>Sign up/sign in with: <code className="bg-gray-100 px-2 py-1 rounded"><EMAIL></code></p>
                </div>
                
                <div>
                  <h4 className="font-semibold mb-2">Method 2: Add Your Email</h4>
                  <p>Edit <code className="bg-gray-100 px-2 py-1 rounded">src/contexts/AdminContext.tsx</code> and add your email to the ADMIN_EMAILS array.</p>
                </div>
                
                <div>
                  <h4 className="font-semibold mb-2">Method 3: Direct URL</h4>
                  <p>Once you're an admin, go to: <code className="bg-gray-100 px-2 py-1 rounded">http://localhost:8080/admin</code></p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default AdminTest;
