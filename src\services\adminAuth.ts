import { supabase } from '@/integrations/supabase/client';

export interface AdminUser {
  id: string;
  email: string;
  full_name: string;
  role: 'admin' | 'super_admin';
  is_active: boolean;
  last_login?: string;
  created_at: string;
}

export interface AdminAuthResponse {
  user: AdminUser | null;
  error: string | null;
}

export interface AdminStats {
  totalUsers: number;
  totalCourses: number;
  totalEnrollments: number;
  pendingPayments: number;
  approvedPayments: number;
  totalRevenue: number;
  activeUsers: number;
  featuredCourses: number;
}

class AdminAuthService {
  private currentAdmin: AdminUser | null = null;

  // Authenticate admin user
  async signIn(email: string, password: string): Promise<AdminAuthResponse> {
    try {
      const { data, error } = await supabase.rpc('authenticate_admin', {
        email_input: email,
        password_input: password
      });

      if (error) {
        console.error('Admin authentication error:', error);
        return { user: null, error: error.message };
      }

      if (!data || data.length === 0) {
        return { user: null, error: 'Invalid email or password' };
      }

      const adminUser = data[0] as AdminUser;
      this.currentAdmin = adminUser;

      // Log the login activity
      await this.logActivity('admin_login', 'auth', adminUser.id, {
        email: adminUser.email,
        timestamp: new Date().toISOString()
      });

      return { user: adminUser, error: null };
    } catch (error) {
      console.error('Admin sign in error:', error);
      return { user: null, error: 'Authentication failed' };
    }
  }

  // Get current admin user
  getCurrentAdmin(): AdminUser | null {
    return this.currentAdmin;
  }

  // Sign out admin
  async signOut(): Promise<void> {
    if (this.currentAdmin) {
      await this.logActivity('admin_logout', 'auth', this.currentAdmin.id);
    }
    this.currentAdmin = null;
  }

  // Check if user is admin
  isAdmin(): boolean {
    return this.currentAdmin !== null && this.currentAdmin.is_active;
  }

  // Get admin dashboard statistics
  async getDashboardStats(): Promise<AdminStats> {
    try {
      const [
        usersResult,
        coursesResult,
        enrollmentsResult,
        paymentsResult,
        activeUsersResult,
        featuredCoursesResult
      ] = await Promise.all([
        supabase.from('profiles').select('id', { count: 'exact' }),
        supabase.from('courses').select('id', { count: 'exact' }),
        supabase.from('enrollments').select('id', { count: 'exact' }),
        supabase.from('payment_requests').select('*'),
        supabase.from('profiles').select('id', { count: 'exact' }).gte('updated_at', new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString()),
        supabase.from('courses').select('id', { count: 'exact' }).eq('is_featured', true)
      ]);

      const payments = paymentsResult.data || [];
      const pendingPayments = payments.filter(p => p.status === 'pending').length;
      const approvedPayments = payments.filter(p => p.status === 'approved').length;
      const totalRevenue = payments
        .filter(p => p.status === 'approved')
        .reduce((sum, p) => sum + (p.amount || 0), 0);

      return {
        totalUsers: usersResult.count || 0,
        totalCourses: coursesResult.count || 0,
        totalEnrollments: enrollmentsResult.count || 0,
        pendingPayments,
        approvedPayments,
        totalRevenue,
        activeUsers: activeUsersResult.count || 0,
        featuredCourses: featuredCoursesResult.count || 0
      };
    } catch (error) {
      console.error('Error fetching dashboard stats:', error);
      return {
        totalUsers: 0,
        totalCourses: 0,
        totalEnrollments: 0,
        pendingPayments: 0,
        approvedPayments: 0,
        totalRevenue: 0,
        activeUsers: 0,
        featuredCourses: 0
      };
    }
  }

  // Approve payment request
  async approvePayment(paymentId: string, adminNotes?: string): Promise<{ success: boolean; error?: string }> {
    try {
      if (!this.currentAdmin) {
        return { success: false, error: 'Admin not authenticated' };
      }

      // Get payment details
      const { data: payment, error: paymentError } = await supabase
        .from('payment_requests')
        .select('*, courses(*), profiles(*)')
        .eq('id', paymentId)
        .single();

      if (paymentError || !payment) {
        return { success: false, error: 'Payment request not found' };
      }

      // Update payment status
      const { error: updateError } = await supabase
        .from('payment_requests')
        .update({
          status: 'approved',
          admin_notes: adminNotes,
          processed_by: this.currentAdmin.id,
          processed_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .eq('id', paymentId);

      if (updateError) {
        return { success: false, error: updateError.message };
      }

      // Create enrollment
      const { error: enrollmentError } = await supabase
        .from('enrollments')
        .insert({
          user_id: payment.user_id,
          course_id: payment.course_id,
          status: 'active',
          enrolled_at: new Date().toISOString()
        });

      if (enrollmentError) {
        console.error('Error creating enrollment:', enrollmentError);
        // Don't return error here as payment is already approved
      }

      // Update course student count
      await supabase.rpc('increment_course_students', { course_id: payment.course_id });

      // Log activity
      await this.logActivity('payment_approved', 'payment', paymentId, {
        user_email: payment.profiles.email,
        course_title: payment.courses.title,
        amount: payment.amount,
        admin_notes: adminNotes
      });

      return { success: true };
    } catch (error) {
      console.error('Error approving payment:', error);
      return { success: false, error: 'Failed to approve payment' };
    }
  }

  // Reject payment request
  async rejectPayment(paymentId: string, adminNotes?: string): Promise<{ success: boolean; error?: string }> {
    try {
      if (!this.currentAdmin) {
        return { success: false, error: 'Admin not authenticated' };
      }

      const { error } = await supabase
        .from('payment_requests')
        .update({
          status: 'rejected',
          admin_notes: adminNotes,
          processed_by: this.currentAdmin.id,
          processed_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .eq('id', paymentId);

      if (error) {
        return { success: false, error: error.message };
      }

      // Log activity
      await this.logActivity('payment_rejected', 'payment', paymentId, {
        admin_notes: adminNotes
      });

      return { success: true };
    } catch (error) {
      console.error('Error rejecting payment:', error);
      return { success: false, error: 'Failed to reject payment' };
    }
  }

  // Log admin activity
  async logActivity(
    action: string,
    targetType?: string,
    targetId?: string,
    details?: any
  ): Promise<void> {
    try {
      if (!this.currentAdmin) return;

      await supabase.rpc('log_admin_activity', {
        admin_email: this.currentAdmin.email,
        action_text: action,
        target_type_text: targetType,
        target_id_input: targetId,
        details_input: details
      });
    } catch (error) {
      console.error('Error logging admin activity:', error);
    }
  }

  // Get all payment requests
  async getPaymentRequests() {
    try {
      const { data, error } = await supabase
        .from('payment_requests')
        .select(`
          *,
          courses (title, price, category),
          profiles (full_name, email, country)
        `)
        .order('created_at', { ascending: false });

      if (error) throw error;
      return { data, error: null };
    } catch (error) {
      console.error('Error fetching payment requests:', error);
      return { data: null, error: error.message };
    }
  }

  // Get all users
  async getUsers() {
    try {
      const { data, error } = await supabase
        .from('profiles')
        .select(`
          *,
          enrollments (
            id,
            course_id,
            progress,
            status,
            completed_at,
            courses (title)
          )
        `)
        .order('created_at', { ascending: false });

      if (error) throw error;
      return { data, error: null };
    } catch (error) {
      console.error('Error fetching users:', error);
      return { data: null, error: error.message };
    }
  }

  // Get all enrollments
  async getEnrollments() {
    try {
      const { data, error } = await supabase
        .from('enrollments')
        .select(`
          *,
          courses (title, category, level, price),
          profiles (full_name, email, country)
        `)
        .order('enrolled_at', { ascending: false });

      if (error) throw error;
      return { data, error: null };
    } catch (error) {
      console.error('Error fetching enrollments:', error);
      return { data: null, error: error.message };
    }
  }
}

export const adminAuthService = new AdminAuthService();
