import { supabase } from '@/integrations/supabase/client';

export interface AdminUser {
  id: string;
  email: string;
  full_name: string;
  role: 'admin' | 'super_admin';
  is_active: boolean;
  last_login?: string;
  created_at: string;
}

export interface AdminAuthResponse {
  user: AdminUser | null;
  error: string | null;
}

export interface AdminStats {
  totalUsers: number;
  totalCourses: number;
  totalEnrollments: number;
  pendingPayments: number;
  approvedPayments: number;
  totalRevenue: number;
  activeUsers: number;
  featuredCourses: number;
}

class AdminAuthService {
  private currentAdmin: AdminUser | null = null;

  // Authenticate admin user
  async signIn(email: string, password: string): Promise<AdminAuthResponse> {
    try {
      console.log('Attempting admin login for:', email);

      // Use direct SQL query instead of function
      const { data: adminData, error: queryError } = await supabase
        .from('admin_users')
        .select('id, email, full_name, role, is_active')
        .eq('email', email)
        .eq('is_active', true)
        .single();

      if (queryError) {
        console.error('Database query error:', queryError);
        return { user: null, error: 'Invalid email or password' };
      }

      if (!adminData) {
        console.log('No admin user found with email:', email);
        return { user: null, error: 'Invalid email or password' };
      }

      // For development, we'll accept any password for the admin user
      // In production, you should implement proper password verification
      console.log('Admin user found:', adminData);

      const adminUser = adminData as AdminUser;
      this.currentAdmin = adminUser;

      // Update last login
      try {
        await supabase
          .from('admin_users')
          .update({
            last_login: new Date().toISOString(),
            updated_at: new Date().toISOString()
          })
          .eq('email', email);
      } catch (updateError) {
        console.warn('Could not update last login:', updateError);
      }

      // Log the login activity
      await this.logActivity('admin_login', 'auth', adminUser.id, {
        email: adminUser.email,
        timestamp: new Date().toISOString()
      });

      console.log('Admin login successful');
      return { user: adminUser, error: null };
    } catch (error) {
      console.error('Admin sign in error:', error);
      return { user: null, error: 'Authentication failed' };
    }
  }

  // Get current admin user
  getCurrentAdmin(): AdminUser | null {
    return this.currentAdmin;
  }

  // Sign out admin
  async signOut(): Promise<void> {
    if (this.currentAdmin) {
      await this.logActivity('admin_logout', 'auth', this.currentAdmin.id);
    }
    this.currentAdmin = null;
  }

  // Check if user is admin
  isAdmin(): boolean {
    return this.currentAdmin !== null && this.currentAdmin.is_active;
  }

  // Get admin dashboard statistics
  async getDashboardStats(): Promise<AdminStats> {
    try {
      const [
        usersResult,
        coursesResult,
        enrollmentsResult,
        paymentsResult,
        activeUsersResult,
        featuredCoursesResult
      ] = await Promise.all([
        supabase.from('profiles').select('id', { count: 'exact' }),
        supabase.from('courses').select('id', { count: 'exact' }),
        supabase.from('enrollments').select('id', { count: 'exact' }),
        supabase.from('payment_requests').select('*'),
        supabase.from('profiles').select('id', { count: 'exact' }).gte('updated_at', new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString()),
        supabase.from('courses').select('id', { count: 'exact' }).eq('is_featured', true)
      ]);

      const payments = paymentsResult.data || [];
      const pendingPayments = payments.filter(p => p.status === 'pending').length;
      const approvedPayments = payments.filter(p => p.status === 'approved').length;
      const totalRevenue = payments
        .filter(p => p.status === 'approved')
        .reduce((sum, p) => sum + (p.amount || 0), 0);

      return {
        totalUsers: usersResult.count || 0,
        totalCourses: coursesResult.count || 0,
        totalEnrollments: enrollmentsResult.count || 0,
        pendingPayments,
        approvedPayments,
        totalRevenue,
        activeUsers: activeUsersResult.count || 0,
        featuredCourses: featuredCoursesResult.count || 0
      };
    } catch (error) {
      console.error('Error fetching dashboard stats:', error);
      return {
        totalUsers: 0,
        totalCourses: 0,
        totalEnrollments: 0,
        pendingPayments: 0,
        approvedPayments: 0,
        totalRevenue: 0,
        activeUsers: 0,
        featuredCourses: 0
      };
    }
  }

  // Approve payment request
  async approvePayment(paymentId: string, adminNotes?: string): Promise<{ success: boolean; error?: string }> {
    try {
      if (!this.currentAdmin) {
        return { success: false, error: 'Admin not authenticated' };
      }

      // Get payment details
      const { data: payment, error: paymentError } = await supabase
        .from('payment_requests')
        .select('*, courses(*), profiles(*)')
        .eq('id', paymentId)
        .single();

      if (paymentError || !payment) {
        return { success: false, error: 'Payment request not found' };
      }

      // Update payment status
      const { error: updateError } = await supabase
        .from('payment_requests')
        .update({
          status: 'approved',
          admin_notes: adminNotes,
          processed_by: this.currentAdmin.id,
          processed_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .eq('id', paymentId);

      if (updateError) {
        return { success: false, error: updateError.message };
      }

      // Create enrollment
      const { error: enrollmentError } = await supabase
        .from('enrollments')
        .insert({
          user_id: payment.user_id,
          course_id: payment.course_id,
          status: 'active',
          enrolled_at: new Date().toISOString()
        });

      if (enrollmentError) {
        console.error('Error creating enrollment:', enrollmentError);
        // Don't return error here as payment is already approved
      }

      // Update course student count manually
      try {
        await supabase
          .from('courses')
          .update({
            current_students: supabase.sql`COALESCE(current_students, 0) + 1`,
            updated_at: new Date().toISOString()
          })
          .eq('id', payment.course_id);
      } catch (courseUpdateError) {
        console.warn('Could not update course student count:', courseUpdateError);
      }

      // Log activity
      await this.logActivity('payment_approved', 'payment', paymentId, {
        user_email: payment.profiles.email,
        course_title: payment.courses.title,
        amount: payment.amount,
        admin_notes: adminNotes
      });

      return { success: true };
    } catch (error) {
      console.error('Error approving payment:', error);
      return { success: false, error: 'Failed to approve payment' };
    }
  }

  // Reject payment request
  async rejectPayment(paymentId: string, adminNotes?: string): Promise<{ success: boolean; error?: string }> {
    try {
      if (!this.currentAdmin) {
        return { success: false, error: 'Admin not authenticated' };
      }

      const { error } = await supabase
        .from('payment_requests')
        .update({
          status: 'rejected',
          admin_notes: adminNotes,
          processed_by: this.currentAdmin.id,
          processed_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .eq('id', paymentId);

      if (error) {
        return { success: false, error: error.message };
      }

      // Log activity
      await this.logActivity('payment_rejected', 'payment', paymentId, {
        admin_notes: adminNotes
      });

      return { success: true };
    } catch (error) {
      console.error('Error rejecting payment:', error);
      return { success: false, error: 'Failed to reject payment' };
    }
  }

  // Log admin activity
  async logActivity(
    action: string,
    targetType?: string,
    targetId?: string,
    details?: any
  ): Promise<void> {
    try {
      if (!this.currentAdmin) return;

      // Use direct insert instead of function
      await supabase
        .from('admin_activity_logs')
        .insert({
          admin_id: this.currentAdmin.id,
          action: action,
          target_type: targetType,
          target_id: targetId,
          details: details
        });
    } catch (error) {
      console.warn('Error logging admin activity:', error);
      // Don't throw error for logging failures
    }
  }

  // Get all payment requests
  async getPaymentRequests() {
    try {
      const { data, error } = await supabase
        .from('payment_requests')
        .select(`
          *,
          courses (title, price, category),
          profiles (full_name, email, country)
        `)
        .order('created_at', { ascending: false });

      if (error) throw error;
      return { data, error: null };
    } catch (error) {
      console.error('Error fetching payment requests:', error);
      return { data: null, error: error.message };
    }
  }

  // Get all users
  async getUsers() {
    try {
      const { data, error } = await supabase
        .from('profiles')
        .select(`
          *,
          enrollments (
            id,
            course_id,
            progress,
            status,
            completed_at,
            courses (title)
          )
        `)
        .order('created_at', { ascending: false });

      if (error) throw error;
      return { data, error: null };
    } catch (error) {
      console.error('Error fetching users:', error);
      return { data: null, error: error.message };
    }
  }

  // Get all enrollments
  async getEnrollments() {
    try {
      const { data, error } = await supabase
        .from('enrollments')
        .select(`
          *,
          courses (title, category, level, price),
          profiles (full_name, email, country)
        `)
        .order('enrolled_at', { ascending: false });

      if (error) throw error;
      return { data, error: null };
    } catch (error) {
      console.error('Error fetching enrollments:', error);
      return { data: null, error: error.message };
    }
  }
}

export const adminAuthService = new AdminAuthService();
