import React, { useEffect, useState } from 'react';
import { adminAuthService } from '@/services/adminAuth';
import { supabase } from '@/integrations/supabase/client';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { useToast } from '@/hooks/use-toast';

const AdminDebug = () => {
  const [debugInfo, setDebugInfo] = useState<any>({});
  const [loading, setLoading] = useState(true);
  const { toast } = useToast();

  const runDiagnostics = async () => {
    setLoading(true);
    const info: any = {};

    try {
      // Check admin user
      const admin = adminAuthService.getCurrentAdmin();
      info.currentAdmin = admin;

      // Check database connection
      const { data: connectionTest, error: connectionError } = await supabase
        .from('admin_users')
        .select('count')
        .limit(1);
      
      info.databaseConnection = connectionError ? 'Failed' : 'Success';
      info.connectionError = connectionError?.message;

      // Check tables exist
      const tables = ['profiles', 'courses', 'enrollments', 'payment_requests', 'admin_users'];
      info.tableChecks = {};

      for (const table of tables) {
        try {
          const { count, error } = await supabase
            .from(table)
            .select('*', { count: 'exact', head: true });
          
          info.tableChecks[table] = {
            exists: !error,
            count: count || 0,
            error: error?.message
          };
        } catch (err) {
          info.tableChecks[table] = {
            exists: false,
            count: 0,
            error: err.message
          };
        }
      }

      // Test dashboard stats
      try {
        const stats = await adminAuthService.getDashboardStats();
        info.dashboardStats = stats;
      } catch (err) {
        info.dashboardStatsError = err.message;
      }

      // Test payment requests
      try {
        const { data, error } = await adminAuthService.getPaymentRequests();
        info.paymentRequests = {
          count: data?.length || 0,
          error: error,
          sample: data?.slice(0, 2)
        };
      } catch (err) {
        info.paymentRequestsError = err.message;
      }

    } catch (error) {
      info.generalError = error.message;
    }

    setDebugInfo(info);
    setLoading(false);
  };

  const createSampleData = async () => {
    try {
      // Create sample courses
      const { error: coursesError } = await supabase
        .from('courses')
        .insert([
          {
            title: 'Sample Course 1',
            description: 'This is a sample course for testing',
            category: 'Development',
            level: 'Beginner',
            duration: '4 weeks',
            price: 99.99,
            is_featured: true,
            is_active: true
          },
          {
            title: 'Sample Course 2',
            description: 'Another sample course',
            category: 'Design',
            level: 'Intermediate',
            duration: '6 weeks',
            price: 149.99,
            is_featured: false,
            is_active: true
          }
        ]);

      if (coursesError) {
        console.error('Error creating courses:', coursesError);
      }

      toast({
        title: "Sample Data Created",
        description: "Sample courses have been added to the database",
      });

      // Refresh diagnostics
      runDiagnostics();
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to create sample data",
        variant: "destructive",
      });
    }
  };

  useEffect(() => {
    runDiagnostics();
  }, []);

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-green-50 to-emerald-100 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-green-600 mx-auto mb-4"></div>
          <p className="text-lg text-green-700 font-medium">Running diagnostics...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 to-emerald-100 p-8">
      <div className="max-w-6xl mx-auto">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-4">Admin Debug Dashboard</h1>
          <div className="flex gap-4">
            <Button onClick={runDiagnostics}>Refresh Diagnostics</Button>
            <Button onClick={createSampleData} variant="outline">Create Sample Data</Button>
            <Button asChild variant="outline">
              <a href="/admin">Go to Admin Panel</a>
            </Button>
          </div>
        </div>

        <div className="grid gap-6">
          {/* Current Admin */}
          <Card>
            <CardHeader>
              <CardTitle>Current Admin User</CardTitle>
            </CardHeader>
            <CardContent>
              <pre className="bg-gray-100 p-4 rounded text-sm overflow-auto">
                {JSON.stringify(debugInfo.currentAdmin, null, 2)}
              </pre>
            </CardContent>
          </Card>

          {/* Database Connection */}
          <Card>
            <CardHeader>
              <CardTitle>Database Connection</CardTitle>
            </CardHeader>
            <CardContent>
              <p className={`font-semibold ${debugInfo.databaseConnection === 'Success' ? 'text-green-600' : 'text-red-600'}`}>
                Status: {debugInfo.databaseConnection}
              </p>
              {debugInfo.connectionError && (
                <p className="text-red-600 mt-2">Error: {debugInfo.connectionError}</p>
              )}
            </CardContent>
          </Card>

          {/* Table Checks */}
          <Card>
            <CardHeader>
              <CardTitle>Database Tables</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {Object.entries(debugInfo.tableChecks || {}).map(([table, info]: [string, any]) => (
                  <div key={table} className="border rounded p-3">
                    <h4 className="font-semibold">{table}</h4>
                    <p className={info.exists ? 'text-green-600' : 'text-red-600'}>
                      {info.exists ? '✅ Exists' : '❌ Missing'}
                    </p>
                    <p>Records: {info.count}</p>
                    {info.error && <p className="text-red-600 text-xs">{info.error}</p>}
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Dashboard Stats */}
          <Card>
            <CardHeader>
              <CardTitle>Dashboard Statistics</CardTitle>
            </CardHeader>
            <CardContent>
              {debugInfo.dashboardStats ? (
                <pre className="bg-gray-100 p-4 rounded text-sm overflow-auto">
                  {JSON.stringify(debugInfo.dashboardStats, null, 2)}
                </pre>
              ) : (
                <p className="text-red-600">Error: {debugInfo.dashboardStatsError}</p>
              )}
            </CardContent>
          </Card>

          {/* Payment Requests */}
          <Card>
            <CardHeader>
              <CardTitle>Payment Requests</CardTitle>
            </CardHeader>
            <CardContent>
              {debugInfo.paymentRequests ? (
                <div>
                  <p>Count: {debugInfo.paymentRequests.count}</p>
                  {debugInfo.paymentRequests.error && (
                    <p className="text-red-600">Error: {debugInfo.paymentRequests.error}</p>
                  )}
                  {debugInfo.paymentRequests.sample && (
                    <pre className="bg-gray-100 p-4 rounded text-sm overflow-auto mt-2">
                      {JSON.stringify(debugInfo.paymentRequests.sample, null, 2)}
                    </pre>
                  )}
                </div>
              ) : (
                <p className="text-red-600">Error: {debugInfo.paymentRequestsError}</p>
              )}
            </CardContent>
          </Card>

          {/* Full Debug Info */}
          <Card>
            <CardHeader>
              <CardTitle>Full Debug Information</CardTitle>
            </CardHeader>
            <CardContent>
              <pre className="bg-gray-100 p-4 rounded text-sm overflow-auto max-h-96">
                {JSON.stringify(debugInfo, null, 2)}
              </pre>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default AdminDebug;
