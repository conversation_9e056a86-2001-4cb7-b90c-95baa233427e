import { useAuth } from "../contexts/AuthContext";
import { Navigate } from "react-router-dom";

const ProtectedAdminRoute = ({ children }: { children: JSX.Element }) => {
  const { user, loading, role } = useAuth();

  if (loading) return <div>Loading...</div>;

  if (!user) return <Navigate to="/auth" />;

  if (role !== "admin") return <Navigate to="/dashboard" />;

  return children;
};

export default ProtectedAdminRoute;
