-- Wisdom Tree Academy Admin Database Setup
-- Run these SQL commands in your Supabase SQL editor

-- 1. Create admin_users table for admin authentication
CREATE TABLE IF NOT EXISTS admin_users (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  email TEXT UNIQUE NOT NULL,
  password_hash TEXT NOT NULL,
  full_name TEXT,
  role TEXT DEFAULT 'admin' CHECK (role IN ('admin', 'super_admin')),
  is_active BOOLEAN DEFAULT true,
  last_login TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 2. Create or update profiles table
CREATE TABLE IF NOT EXISTS profiles (
  id UUID REFERENCES auth.users(id) PRIMARY KEY,
  full_name TEXT,
  email TEXT,
  country TEXT,
  phone TEXT,
  avatar_url TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 3. Create or update courses table
CREATE TABLE IF NOT EXISTS courses (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  title TEXT NOT NULL,
  description TEXT,
  category TEXT NOT NULL,
  level TEXT NOT NULL CHECK (level IN ('Beginner', 'Intermediate', 'Advanced')),
  duration TEXT,
  price DECIMAL(10,2) DEFAULT 0,
  is_featured BOOLEAN DEFAULT FALSE,
  is_active BOOLEAN DEFAULT TRUE,
  image_url TEXT,
  instructor_id UUID REFERENCES profiles(id),
  max_students INTEGER,
  current_students INTEGER DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 4. Create or update enrollments table
CREATE TABLE IF NOT EXISTS enrollments (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
  course_id UUID REFERENCES courses(id) ON DELETE CASCADE,
  progress INTEGER DEFAULT 0 CHECK (progress >= 0 AND progress <= 100),
  status TEXT DEFAULT 'active' CHECK (status IN ('active', 'completed', 'suspended', 'cancelled')),
  enrolled_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  completed_at TIMESTAMP WITH TIME ZONE,
  last_accessed TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(user_id, course_id)
);

-- 5. Create or update payment_requests table
CREATE TABLE IF NOT EXISTS payment_requests (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
  course_id UUID REFERENCES courses(id) ON DELETE CASCADE,
  amount DECIMAL(10,2) NOT NULL,
  payment_method TEXT NOT NULL,
  sender_number TEXT,
  transaction_id TEXT NOT NULL,
  payment_proof_url TEXT,
  status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'approved', 'rejected', 'refunded')),
  admin_notes TEXT,
  processed_by UUID REFERENCES admin_users(id),
  processed_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 6. Create admin_activity_logs table for tracking admin actions
CREATE TABLE IF NOT EXISTS admin_activity_logs (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  admin_id UUID REFERENCES admin_users(id),
  action TEXT NOT NULL,
  target_type TEXT, -- 'course', 'user', 'payment', etc.
  target_id UUID,
  details JSONB,
  ip_address INET,
  user_agent TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 7. Create system_settings table for admin configuration
CREATE TABLE IF NOT EXISTS system_settings (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  setting_key TEXT UNIQUE NOT NULL,
  setting_value JSONB,
  description TEXT,
  updated_by UUID REFERENCES admin_users(id),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 8. Insert the admin user with hashed password
-- Note: In production, you should hash the password properly
INSERT INTO admin_users (email, password_hash, full_name, role, is_active) 
VALUES (
  '<EMAIL>',
  crypt('samsun@55', gen_salt('bf')), -- Using bcrypt hashing
  'System Administrator',
  'super_admin',
  true
) ON CONFLICT (email) DO UPDATE SET
  password_hash = crypt('samsun@55', gen_salt('bf')),
  updated_at = NOW();

-- 9. Enable Row Level Security
ALTER TABLE admin_users ENABLE ROW LEVEL SECURITY;
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE courses ENABLE ROW LEVEL SECURITY;
ALTER TABLE enrollments ENABLE ROW LEVEL SECURITY;
ALTER TABLE payment_requests ENABLE ROW LEVEL SECURITY;
ALTER TABLE admin_activity_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE system_settings ENABLE ROW LEVEL SECURITY;

-- 10. Create RLS Policies for admin access
-- Admin users can access their own records
CREATE POLICY "Admin users can view own record" ON admin_users
  FOR SELECT USING (auth.jwt() ->> 'email' = email);

-- Profiles policies (public read for courses, admin full access)
CREATE POLICY "Anyone can view profiles" ON profiles
  FOR SELECT USING (true);

CREATE POLICY "Users can update own profile" ON profiles
  FOR UPDATE USING (auth.uid() = id);

CREATE POLICY "Users can insert own profile" ON profiles
  FOR INSERT WITH CHECK (auth.uid() = id);

-- Courses policies (public read, admin write)
CREATE POLICY "Anyone can view active courses" ON courses
  FOR SELECT USING (is_active = true);

-- Payment requests policies
CREATE POLICY "Users can view own payment requests" ON payment_requests
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own payment requests" ON payment_requests
  FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Enrollments policies
CREATE POLICY "Users can view own enrollments" ON enrollments
  FOR SELECT USING (auth.uid() = user_id);

-- 11. Create functions for admin authentication
CREATE OR REPLACE FUNCTION authenticate_admin(email_input TEXT, password_input TEXT)
RETURNS TABLE(
  id UUID,
  email TEXT,
  full_name TEXT,
  role TEXT,
  is_active BOOLEAN
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    au.id,
    au.email,
    au.full_name,
    au.role,
    au.is_active
  FROM admin_users au
  WHERE au.email = email_input 
    AND au.password_hash = crypt(password_input, au.password_hash)
    AND au.is_active = true;
    
  -- Update last login
  UPDATE admin_users 
  SET last_login = NOW(), updated_at = NOW()
  WHERE admin_users.email = email_input;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 12. Create function to log admin activities
CREATE OR REPLACE FUNCTION log_admin_activity(
  admin_email TEXT,
  action_text TEXT,
  target_type_text TEXT DEFAULT NULL,
  target_id_input UUID DEFAULT NULL,
  details_input JSONB DEFAULT NULL
) RETURNS VOID AS $$
DECLARE
  admin_id_var UUID;
BEGIN
  -- Get admin ID from email
  SELECT id INTO admin_id_var FROM admin_users WHERE email = admin_email;
  
  IF admin_id_var IS NOT NULL THEN
    INSERT INTO admin_activity_logs (admin_id, action, target_type, target_id, details)
    VALUES (admin_id_var, action_text, target_type_text, target_id_input, details_input);
  END IF;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 13. Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_admin_users_email ON admin_users(email);
CREATE INDEX IF NOT EXISTS idx_admin_users_active ON admin_users(is_active);
CREATE INDEX IF NOT EXISTS idx_payment_requests_status ON payment_requests(status);
CREATE INDEX IF NOT EXISTS idx_payment_requests_user ON payment_requests(user_id);
CREATE INDEX IF NOT EXISTS idx_enrollments_user ON enrollments(user_id);
CREATE INDEX IF NOT EXISTS idx_enrollments_course ON enrollments(course_id);
CREATE INDEX IF NOT EXISTS idx_enrollments_status ON enrollments(status);
CREATE INDEX IF NOT EXISTS idx_courses_active ON courses(is_active);
CREATE INDEX IF NOT EXISTS idx_courses_featured ON courses(is_featured);
CREATE INDEX IF NOT EXISTS idx_admin_activity_logs_admin ON admin_activity_logs(admin_id);
CREATE INDEX IF NOT EXISTS idx_admin_activity_logs_created ON admin_activity_logs(created_at);

-- 14. Insert some sample courses
INSERT INTO courses (title, description, category, level, duration, price, is_featured, is_active) VALUES
('Full Stack Web Development', 'Learn to build complete web applications from frontend to backend using modern technologies', 'Development', 'Intermediate', '12 weeks', 299.99, true, true),
('Cybersecurity Fundamentals', 'Master the basics of cybersecurity, ethical hacking, and network security', 'Security', 'Beginner', '8 weeks', 199.99, true, true),
('Machine Learning with Python', 'Dive into AI and machine learning using Python, TensorFlow, and scikit-learn', 'Artificial Intelligence', 'Advanced', '16 weeks', 399.99, true, true),
('UI/UX Design Masterclass', 'Create beautiful and user-friendly interfaces with modern design principles', 'Design', 'Intermediate', '10 weeks', 249.99, false, true),
('Digital Marketing Strategy', 'Learn modern digital marketing techniques and social media strategies', 'Marketing', 'Beginner', '6 weeks', 149.99, false, true),
('React.js Complete Course', 'Master React.js from basics to advanced concepts including hooks and context', 'Development', 'Intermediate', '8 weeks', 199.99, true, true),
('Data Science with Python', 'Learn data analysis, visualization, and statistical modeling with Python', 'Data Science', 'Intermediate', '14 weeks', 349.99, true, true),
('Mobile App Development', 'Build cross-platform mobile apps using React Native and Flutter', 'Development', 'Advanced', '12 weeks', 299.99, false, true)
ON CONFLICT DO NOTHING;

-- 15. Insert default system settings
INSERT INTO system_settings (setting_key, setting_value, description) VALUES
('site_name', '"Wisdom Tree Academy"', 'Website name'),
('max_enrollment_per_course', '100', 'Maximum students per course'),
('payment_methods', '["bkash", "nagad", "rocket", "bank_transfer"]', 'Accepted payment methods'),
('auto_approve_payments', 'false', 'Automatically approve payments'),
('email_notifications', 'true', 'Send email notifications'),
('maintenance_mode', 'false', 'Enable maintenance mode')
ON CONFLICT (setting_key) DO NOTHING;

-- 16. Grant necessary permissions
GRANT USAGE ON SCHEMA public TO anon, authenticated;
GRANT ALL ON ALL TABLES IN SCHEMA public TO authenticated;
GRANT ALL ON ALL SEQUENCES IN SCHEMA public TO authenticated;

-- Enable the pgcrypto extension for password hashing
CREATE EXTENSION IF NOT EXISTS pgcrypto;
