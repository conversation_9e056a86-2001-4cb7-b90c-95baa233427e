import React, { useEffect, useState } from 'react';
import { Navigate, Link } from 'react-router-dom';
import { adminAuthService, AdminStats } from '@/services/adminAuth';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Users,
  BookOpen,
  DollarSign,
  Clock,
  CheckCircle,
  XCircle,
  Plus,
  Home,
  LogOut,
  TrendingUp,
  AlertCircle,
  Activity,
  Star
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import CourseManagement from '@/components/admin/CourseManagement';
import UserManagement from '@/components/admin/UserManagement';
import EnrollmentManagement from '@/components/admin/EnrollmentManagement';

interface PaymentRequest {
  id: string;
  amount: number;
  course_id: string;
  payment_method: string;
  sender_number: string;
  status: string;
  transaction_id: string;
  created_at: string;
  user_id: string;
  courses: {
    title: string;
    price: number;
  };
  profiles: {
    full_name: string;
    email: string;
  };
}

const AdminDashboard = () => {
  // All hooks must be called unconditionally at the top
  const { toast } = useToast();
  const navigate = useNavigate();
  const [stats, setStats] = useState<AdminStats>({
    totalUsers: 0,
    totalCourses: 0,
    totalEnrollments: 0,
    pendingPayments: 0,
    totalRevenue: 0,
    approvedPayments: 0,
    activeUsers: 0,
    featuredCourses: 0
  });
  const [paymentRequests, setPaymentRequests] = useState<PaymentRequest[]>([]);
  const [loading, setLoading] = useState(true);
  const [currentAdmin, setCurrentAdmin] = useState(adminAuthService.getCurrentAdmin());

  useEffect(() => {
    // Check if admin is logged in
    const admin = adminAuthService.getCurrentAdmin();
    if (admin) {
      setCurrentAdmin(admin);
      fetchAdminData();
    }
  }, []);

  useEffect(() => {
    if (currentAdmin) {
      fetchAdminData();
    }
  }, [currentAdmin]);

  // Redirect if not admin - moved after all hooks
  if (!loading && !currentAdmin) {
    return <Navigate to="/auth" replace />;
  }

  const fetchAdminData = async () => {
    try {
      setLoading(true);

      // Fetch dashboard stats using admin service
      const dashboardStats = await adminAuthService.getDashboardStats();
      setStats(dashboardStats);

      // Fetch payment requests
      const { data: payments, error } = await adminAuthService.getPaymentRequests();
      if (error) {
        throw new Error(error);
      }
      setPaymentRequests(payments || []);

    } catch (error) {
      console.error('Error fetching admin data:', error);
      toast({
        title: "Error",
        description: "Failed to load admin data",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleSignOut = async () => {
    await adminAuthService.signOut();
    setCurrentAdmin(null);
    navigate('/auth');
    toast({
      title: "Signed Out",
      description: "You have been successfully signed out",
    });
  };

  const handlePaymentAction = async (paymentId: string, action: 'approve' | 'reject', adminNotes?: string) => {
    try {
      let result;
      if (action === 'approve') {
        result = await adminAuthService.approvePayment(paymentId, adminNotes);
      } else {
        result = await adminAuthService.rejectPayment(paymentId, adminNotes);
      }

      if (!result.success) {
        throw new Error(result.error);
      }

      toast({
        title: action === 'approve' ? "Payment Approved" : "Payment Rejected",
        description: `Payment has been ${action}d successfully`,
      });

      // Refresh data
      fetchAdminData();
    } catch (error) {
      console.error(`Error ${action}ing payment:`, error);
      toast({
        title: "Error",
        description: `Failed to ${action} payment`,
        variant: "destructive",
      });
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-green-50 to-emerald-100 flex items-center justify-center">
        <div className="text-center">
          <img
            src="/lovable-uploads/4df5a54b-5bf0-4458-a8c5-d397a73b2876.png"
            alt="Wisdom Tree Academy"
            className="h-16 w-16 mx-auto mb-4 animate-pulse"
          />
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-green-600 mx-auto mb-4"></div>
          <p className="text-lg text-green-700 font-medium">Loading admin dashboard...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 to-emerald-100">
      {/* Header */}
      <div className="bg-white/95 backdrop-blur-sm shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <img 
                src="/lovable-uploads/4df5a54b-5bf0-4458-a8c5-d397a73b2876.png" 
                alt="Wisdom Tree Academy" 
                className="h-12 w-12 animate-fade-in"
              />
              <div>
                <h1 className="text-3xl md:text-4xl font-bold text-gray-900 animate-fade-in">
                  Admin Dashboard
                </h1>
                <p className="text-lg text-green-700 animate-fade-in">
                  Welcome, {currentAdmin?.full_name || 'Administrator'} - Manage Wisdom Tree Academy
                </p>
              </div>
            </div>
            <div className="flex items-center gap-3">
              <Button asChild variant="outline" size="lg" className="border-green-600 text-green-600 hover:bg-green-50">
                <Link to="/">
                  <Home className="h-4 w-4 mr-2" />
                  Main Page
                </Link>
              </Button>
              <Button onClick={handleSignOut} size="lg" className="bg-green-600 hover:bg-green-700 text-white">
                <LogOut className="h-4 w-4 mr-2" />
                Sign Out
              </Button>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Stats Overview */}
        <div className="mb-12">
          <div className="text-center mb-8">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">Overview</h2>
            <p className="text-lg text-gray-600">Platform statistics and metrics</p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 xl:grid-cols-8 gap-6">
            <Card className="hover:shadow-lg transition-shadow border-0 shadow-md bg-white">
              <CardContent className="p-6">
                <div className="flex items-center gap-4">
                  <Users className="h-10 w-10 text-blue-600" />
                  <div>
                    <p className="text-2xl font-bold text-gray-900">{stats.totalUsers}</p>
                    <p className="text-sm text-gray-600">Total Users</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="hover:shadow-lg transition-shadow border-0 shadow-md bg-white">
              <CardContent className="p-6">
                <div className="flex items-center gap-4">
                  <Activity className="h-10 w-10 text-green-600" />
                  <div>
                    <p className="text-2xl font-bold text-gray-900">{stats.activeUsers}</p>
                    <p className="text-sm text-gray-600">Active Users</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="hover:shadow-lg transition-shadow border-0 shadow-md bg-white">
              <CardContent className="p-6">
                <div className="flex items-center gap-4">
                  <BookOpen className="h-10 w-10 text-indigo-600" />
                  <div>
                    <p className="text-2xl font-bold text-gray-900">{stats.totalCourses}</p>
                    <p className="text-sm text-gray-600">Total Courses</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="hover:shadow-lg transition-shadow border-0 shadow-md bg-white">
              <CardContent className="p-6">
                <div className="flex items-center gap-4">
                  <Star className="h-10 w-10 text-yellow-600" />
                  <div>
                    <p className="text-2xl font-bold text-gray-900">{stats.featuredCourses}</p>
                    <p className="text-sm text-gray-600">Featured</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="hover:shadow-lg transition-shadow border-0 shadow-md bg-white">
              <CardContent className="p-6">
                <div className="flex items-center gap-4">
                  <TrendingUp className="h-10 w-10 text-purple-600" />
                  <div>
                    <p className="text-2xl font-bold text-gray-900">{stats.totalEnrollments}</p>
                    <p className="text-sm text-gray-600">Enrollments</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="hover:shadow-lg transition-shadow border-0 shadow-md bg-white">
              <CardContent className="p-6">
                <div className="flex items-center gap-4">
                  <Clock className="h-10 w-10 text-orange-600" />
                  <div>
                    <p className="text-2xl font-bold text-gray-900">{stats.pendingPayments}</p>
                    <p className="text-sm text-gray-600">Pending</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="hover:shadow-lg transition-shadow border-0 shadow-md bg-white">
              <CardContent className="p-6">
                <div className="flex items-center gap-4">
                  <CheckCircle className="h-10 w-10 text-green-600" />
                  <div>
                    <p className="text-2xl font-bold text-gray-900">{stats.approvedPayments}</p>
                    <p className="text-sm text-gray-600">Approved</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="hover:shadow-lg transition-shadow border-0 shadow-md bg-white">
              <CardContent className="p-6">
                <div className="flex items-center gap-4">
                  <DollarSign className="h-10 w-10 text-green-600" />
                  <div>
                    <p className="text-2xl font-bold text-gray-900">${stats.totalRevenue}</p>
                    <p className="text-sm text-gray-600">Revenue</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Management Tabs */}
        <Tabs defaultValue="payments" className="w-full">
          <TabsList className="grid w-full grid-cols-4 mb-8">
            <TabsTrigger value="payments">Payment Requests</TabsTrigger>
            <TabsTrigger value="users">Users</TabsTrigger>
            <TabsTrigger value="courses">Courses</TabsTrigger>
            <TabsTrigger value="enrollments">Enrollments</TabsTrigger>
          </TabsList>

          <TabsContent value="payments">
            <Card className="bg-white shadow-md border-0">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <AlertCircle className="h-5 w-5 text-orange-600" />
                  Payment Requests
                </CardTitle>
                <CardDescription>
                  Review and approve/reject payment submissions
                </CardDescription>
              </CardHeader>
              <CardContent>
                {paymentRequests.length === 0 ? (
                  <div className="text-center py-12">
                    <DollarSign className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <h3 className="text-lg font-semibold text-gray-900 mb-2">No payment requests</h3>
                    <p className="text-gray-600">All payment requests have been processed</p>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {paymentRequests.map((payment) => (
                      <Card key={payment.id} className="border border-gray-200">
                        <CardContent className="p-6">
                          <div className="flex items-start justify-between">
                            <div className="flex-1">
                              <div className="flex items-center gap-3 mb-3">
                                <h3 className="text-lg font-semibold text-gray-900">
                                  {payment.courses.title}
                                </h3>
                                <Badge
                                  variant={
                                    payment.status === 'pending' ? 'secondary' :
                                    payment.status === 'approved' ? 'default' : 'destructive'
                                  }
                                  className={
                                    payment.status === 'pending' ? 'bg-orange-100 text-orange-700' :
                                    payment.status === 'approved' ? 'bg-green-100 text-green-700' :
                                    'bg-red-100 text-red-700'
                                  }
                                >
                                  {payment.status.charAt(0).toUpperCase() + payment.status.slice(1)}
                                </Badge>
                              </div>

                              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                                <div>
                                  <p className="text-gray-600">Student</p>
                                  <p className="font-medium">{payment.profiles.full_name}</p>
                                  <p className="text-gray-500">{payment.profiles.email}</p>
                                </div>
                                <div>
                                  <p className="text-gray-600">Amount</p>
                                  <p className="font-medium text-green-600">${payment.amount}</p>
                                </div>
                                <div>
                                  <p className="text-gray-600">Payment Method</p>
                                  <p className="font-medium capitalize">{payment.payment_method}</p>
                                  <p className="text-gray-500">{payment.sender_number}</p>
                                </div>
                                <div>
                                  <p className="text-gray-600">Transaction ID</p>
                                  <p className="font-medium font-mono text-sm">{payment.transaction_id}</p>
                                  <p className="text-gray-500">
                                    {new Date(payment.created_at).toLocaleDateString()}
                                  </p>
                                </div>
                              </div>
                            </div>

                            {payment.status === 'pending' && (
                              <div className="flex gap-2 ml-4">
                                <Button
                                  size="sm"
                                  onClick={() => handlePaymentAction(payment.id, 'approve')}
                                  className="bg-green-600 hover:bg-green-700 text-white"
                                >
                                  <CheckCircle className="h-4 w-4 mr-1" />
                                  Approve
                                </Button>
                                <Button
                                  size="sm"
                                  variant="destructive"
                                  onClick={() => handlePaymentAction(payment.id, 'reject')}
                                >
                                  <XCircle className="h-4 w-4 mr-1" />
                                  Reject
                                </Button>
                              </div>
                            )}
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="users">
            <Card className="bg-white shadow-md border-0">
              <CardContent className="p-6">
                <UserManagement />
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="courses">
            <Card className="bg-white shadow-md border-0">
              <CardContent className="p-6">
                <CourseManagement />
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="enrollments">
            <Card className="bg-white shadow-md border-0">
              <CardContent className="p-6">
                <EnrollmentManagement />
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};

export default AdminDashboard;
