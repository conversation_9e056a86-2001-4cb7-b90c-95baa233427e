# Admin Panel Database Setup Instructions

## 🚀 Quick Setup Guide

### Step 1: Access Supabase SQL Editor
1. Go to your Supabase project dashboard
2. Navigate to **SQL Editor** in the left sidebar
3. Click **"New Query"**

### Step 2: Run the Database Setup Script
1. Copy the entire content from `admin-database-setup.sql`
2. Paste it into the SQL Editor
3. Click **"Run"** to execute the script

### Step 3: Verify Setup
After running the script, you should see:
- ✅ All tables created successfully
- ✅ Admin user `<EMAIL>` added
- ✅ Sample courses inserted
- ✅ RLS policies enabled

### Step 4: Test Admin Login
1. Go to `http://localhost:8080/auth`
2. Click the **"Admin"** tab
3. Use credentials:
   - **Email**: `<EMAIL>`
   - **Password**: `samsun@55`
4. You should be redirected to the admin dashboard

## 📊 Database Schema Overview

### Core Tables Created:

#### 1. `admin_users`
- Stores admin authentication data
- Includes roles (admin, super_admin)
- Password hashing with bcrypt

#### 2. `profiles`
- User profile information
- Links to Supabase auth.users

#### 3. `courses`
- Course catalog management
- Pricing, categories, levels
- Featured course flags

#### 4. `enrollments`
- Student course enrollments
- Progress tracking
- Status management

#### 5. `payment_requests`
- Payment processing workflow
- Admin approval system
- Transaction tracking

#### 6. `admin_activity_logs`
- Audit trail for admin actions
- Security monitoring
- Activity tracking

#### 7. `system_settings`
- Configurable system parameters
- Admin-controlled settings

## 🔐 Security Features

### Row Level Security (RLS)
- All tables have RLS enabled
- Proper access policies configured
- Admin-only access where needed

### Password Security
- Bcrypt hashing for admin passwords
- Secure authentication functions
- Session management

### Activity Logging
- All admin actions logged
- IP address tracking
- User agent recording

## 🎯 Admin Panel Features

### Dashboard Overview
- Real-time statistics
- User metrics
- Revenue tracking
- Course analytics

### Payment Management
- Approve/reject payments
- Transaction history
- Revenue reports
- Automated enrollment

### User Management
- View all users
- User statistics
- Enrollment tracking
- Activity monitoring

### Course Management
- Add/edit/delete courses
- Pricing management
- Featured course selection
- Category organization

### Enrollment Management
- Track student progress
- Enrollment statistics
- Completion rates
- Status management

## 🛠️ Admin Functions

### Authentication
```sql
-- Admin login function
SELECT * FROM authenticate_admin('email', 'password');
```

### Activity Logging
```sql
-- Log admin actions
SELECT log_admin_activity('<EMAIL>', 'action', 'target_type', 'target_id', '{"key": "value"}');
```

### Course Management
- Automatic student count updates
- Enrollment creation on payment approval
- Featured course management

## 📈 Sample Data Included

### Courses
- Full Stack Web Development ($299.99)
- Cybersecurity Fundamentals ($199.99)
- Machine Learning with Python ($399.99)
- UI/UX Design Masterclass ($249.99)
- Digital Marketing Strategy ($149.99)
- React.js Complete Course ($199.99)
- Data Science with Python ($349.99)
- Mobile App Development ($299.99)

### System Settings
- Site configuration
- Payment methods
- Email notifications
- Maintenance mode

## 🔧 Troubleshooting

### Common Issues:

#### 1. Admin Login Fails
- Check if admin user exists in `admin_users` table
- Verify password hash is correct
- Ensure `is_active = true`

#### 2. RLS Errors
- Verify RLS policies are created
- Check user permissions
- Ensure proper authentication

#### 3. Missing Data
- Re-run the setup script
- Check for SQL errors in logs
- Verify table creation

### SQL Queries for Debugging:

```sql
-- Check admin user
SELECT * FROM admin_users WHERE email = '<EMAIL>';

-- Check tables exist
SELECT table_name FROM information_schema.tables WHERE table_schema = 'public';

-- Check RLS policies
SELECT * FROM pg_policies WHERE schemaname = 'public';
```

## 🎉 Success Indicators

You'll know the setup is successful when:

1. ✅ Admin login works at `/auth` → Admin tab
2. ✅ Dashboard shows statistics
3. ✅ Payment management is functional
4. ✅ Course management works
5. ✅ User management displays data
6. ✅ Activity logging is working

## 📞 Support

If you encounter issues:
1. Check the browser console for errors
2. Verify Supabase connection
3. Review SQL execution logs
4. Ensure all environment variables are set

The admin panel is now fully integrated with Supabase and ready for production use!
