import React, { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Search, BookOpen, Calendar, TrendingUp, User } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

interface Enrollment {
  id: string;
  user_id: string;
  course_id: string;
  progress: number | null;
  enrolled_at: string | null;
  completed_at: string | null;
  courses: {
    title: string;
    category: string;
    level: string;
    price: number | null;
  };
  profiles: {
    full_name: string | null;
    email: string | null;
    country: string | null;
  };
}

const EnrollmentManagement = () => {
  const { toast } = useToast();
  const [enrollments, setEnrollments] = useState<Enrollment[]>([]);
  const [filteredEnrollments, setFilteredEnrollments] = useState<Enrollment[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [categoryFilter, setCategoryFilter] = useState<string>('all');

  useEffect(() => {
    fetchEnrollments();
  }, []);

  useEffect(() => {
    filterEnrollments();
  }, [enrollments, searchTerm, statusFilter, categoryFilter]);

  const fetchEnrollments = async () => {
    try {
      const { data, error } = await supabase
        .from('enrollments')
        .select(`
          *,
          courses (title, category, level, price),
          profiles (full_name, email, country)
        `)
        .order('enrolled_at', { ascending: false });

      if (error) throw error;
      setEnrollments(data || []);
    } catch (error) {
      console.error('Error fetching enrollments:', error);
      toast({
        title: "Error",
        description: "Failed to load enrollments",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const filterEnrollments = () => {
    let filtered = enrollments;

    // Search filter
    if (searchTerm) {
      filtered = filtered.filter(enrollment => 
        enrollment.courses.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        enrollment.profiles.full_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        enrollment.profiles.email?.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Status filter
    if (statusFilter !== 'all') {
      filtered = filtered.filter(enrollment => {
        if (statusFilter === 'completed') return enrollment.completed_at;
        if (statusFilter === 'in-progress') return !enrollment.completed_at && (enrollment.progress || 0) > 0;
        if (statusFilter === 'not-started') return !enrollment.completed_at && (enrollment.progress || 0) === 0;
        return true;
      });
    }

    // Category filter
    if (categoryFilter !== 'all') {
      filtered = filtered.filter(enrollment => 
        enrollment.courses.category === categoryFilter
      );
    }

    setFilteredEnrollments(filtered);
  };

  const getStatusBadge = (enrollment: Enrollment) => {
    if (enrollment.completed_at) {
      return <Badge className="bg-green-100 text-green-700">Completed</Badge>;
    }
    if ((enrollment.progress || 0) > 0) {
      return <Badge className="bg-blue-100 text-blue-700">In Progress</Badge>;
    }
    return <Badge className="bg-gray-100 text-gray-700">Not Started</Badge>;
  };

  const getInitials = (name: string | null) => {
    if (!name) return 'U';
    return name.split(' ').map(n => n[0]).join('').toUpperCase().slice(0, 2);
  };

  const getUniqueCategories = () => {
    const categories = enrollments.map(e => e.courses.category);
    return [...new Set(categories)];
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center py-12">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-green-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h3 className="text-lg font-semibold">Enrollment Management</h3>
          <p className="text-gray-600">View all course enrollments and progress</p>
        </div>
        <Badge variant="secondary" className="text-sm">
          {filteredEnrollments.length} enrollments
        </Badge>
      </div>

      {/* Filters */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
          <Input
            placeholder="Search enrollments..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>
        <Select value={statusFilter} onValueChange={setStatusFilter}>
          <SelectTrigger className="w-full sm:w-48">
            <SelectValue placeholder="Filter by status" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Status</SelectItem>
            <SelectItem value="completed">Completed</SelectItem>
            <SelectItem value="in-progress">In Progress</SelectItem>
            <SelectItem value="not-started">Not Started</SelectItem>
          </SelectContent>
        </Select>
        <Select value={categoryFilter} onValueChange={setCategoryFilter}>
          <SelectTrigger className="w-full sm:w-48">
            <SelectValue placeholder="Filter by category" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Categories</SelectItem>
            {getUniqueCategories().map(category => (
              <SelectItem key={category} value={category}>{category}</SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      {filteredEnrollments.length === 0 ? (
        <div className="text-center py-12">
          <BookOpen className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-gray-900 mb-2">
            {searchTerm || statusFilter !== 'all' || categoryFilter !== 'all' 
              ? 'No enrollments found' 
              : 'No enrollments yet'
            }
          </h3>
          <p className="text-gray-600">
            {searchTerm || statusFilter !== 'all' || categoryFilter !== 'all'
              ? 'Try adjusting your filters'
              : 'Enrollments will appear here once users enroll in courses'
            }
          </p>
        </div>
      ) : (
        <div className="space-y-4">
          {filteredEnrollments.map((enrollment) => (
            <Card key={enrollment.id} className="hover:shadow-lg transition-shadow">
              <CardContent className="p-6">
                <div className="flex flex-col lg:flex-row lg:items-center gap-4">
                  {/* Student Info */}
                  <div className="flex items-center gap-3 flex-1">
                    <Avatar className="h-10 w-10">
                      <AvatarFallback className="bg-blue-100 text-blue-700 font-semibold">
                        {getInitials(enrollment.profiles.full_name)}
                      </AvatarFallback>
                    </Avatar>
                    <div className="min-w-0 flex-1">
                      <h3 className="font-semibold text-gray-900 truncate">
                        {enrollment.profiles.full_name || 'Unknown User'}
                      </h3>
                      <p className="text-sm text-gray-600 truncate">
                        {enrollment.profiles.email}
                      </p>
                      {enrollment.profiles.country && (
                        <p className="text-xs text-gray-500">{enrollment.profiles.country}</p>
                      )}
                    </div>
                  </div>

                  {/* Course Info */}
                  <div className="flex-1">
                    <h4 className="font-semibold text-gray-900 mb-1">
                      {enrollment.courses.title}
                    </h4>
                    <div className="flex flex-wrap gap-2 mb-2">
                      <Badge variant="secondary" className="text-xs">
                        {enrollment.courses.category}
                      </Badge>
                      <Badge variant="outline" className="text-xs">
                        {enrollment.courses.level}
                      </Badge>
                      {getStatusBadge(enrollment)}
                    </div>
                    <p className="text-sm text-green-600 font-medium">
                      ${enrollment.courses.price || 0}
                    </p>
                  </div>

                  {/* Progress Info */}
                  <div className="flex-1">
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-sm text-gray-600">Progress</span>
                      <span className="text-sm font-semibold">
                        {enrollment.progress || 0}%
                      </span>
                    </div>
                    <Progress value={enrollment.progress || 0} className="h-2 mb-2" />
                    <div className="flex items-center gap-4 text-xs text-gray-500">
                      <div className="flex items-center gap-1">
                        <Calendar className="h-3 w-3" />
                        <span>
                          Enrolled: {enrollment.enrolled_at 
                            ? new Date(enrollment.enrolled_at).toLocaleDateString()
                            : 'Unknown'
                          }
                        </span>
                      </div>
                      {enrollment.completed_at && (
                        <div className="flex items-center gap-1">
                          <TrendingUp className="h-3 w-3" />
                          <span>
                            Completed: {new Date(enrollment.completed_at).toLocaleDateString()}
                          </span>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
};

export default EnrollmentManagement;
