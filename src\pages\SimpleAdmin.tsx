import React from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { Navigate } from 'react-router-dom';

const SimpleAdmin = () => {
  // All hooks must be called unconditionally at the top
  const { user } = useAuth();

  // Redirect if not signed in - moved after all hooks
  if (!user) {
    return <Navigate to="/auth" replace />;
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 to-emerald-100 p-8">
      <div className="max-w-6xl mx-auto">
        {/* Header */}
        <div className="bg-white rounded-lg shadow-md p-6 mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            🛡️ Simple Admin Panel
          </h1>
          <p className="text-gray-600">
            Welcome, {user.email}! This is a simplified admin panel for testing.
          </p>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <div className="bg-white rounded-lg shadow-md p-6">
            <div className="flex items-center">
              <div className="p-3 rounded-full bg-blue-100 text-blue-600">
                👥
              </div>
              <div className="ml-4">
                <h3 className="text-lg font-semibold text-gray-900">Users</h3>
                <p className="text-2xl font-bold text-blue-600">0</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-md p-6">
            <div className="flex items-center">
              <div className="p-3 rounded-full bg-green-100 text-green-600">
                📚
              </div>
              <div className="ml-4">
                <h3 className="text-lg font-semibold text-gray-900">Courses</h3>
                <p className="text-2xl font-bold text-green-600">0</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-md p-6">
            <div className="flex items-center">
              <div className="p-3 rounded-full bg-purple-100 text-purple-600">
                📈
              </div>
              <div className="ml-4">
                <h3 className="text-lg font-semibold text-gray-900">Enrollments</h3>
                <p className="text-2xl font-bold text-purple-600">0</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-md p-6">
            <div className="flex items-center">
              <div className="p-3 rounded-full bg-yellow-100 text-yellow-600">
                💰
              </div>
              <div className="ml-4">
                <h3 className="text-lg font-semibold text-gray-900">Revenue</h3>
                <p className="text-2xl font-bold text-yellow-600">$0</p>
              </div>
            </div>
          </div>
        </div>

        {/* Management Sections */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Payment Requests */}
          <div className="bg-white rounded-lg shadow-md p-6">
            <h2 className="text-xl font-bold text-gray-900 mb-4">
              💳 Payment Requests
            </h2>
            <div className="text-center py-8">
              <div className="text-4xl mb-4">📋</div>
              <p className="text-gray-600">No payment requests yet</p>
              <button className="mt-4 bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700">
                Refresh
              </button>
            </div>
          </div>

          {/* Course Management */}
          <div className="bg-white rounded-lg shadow-md p-6">
            <h2 className="text-xl font-bold text-gray-900 mb-4">
              📚 Course Management
            </h2>
            <div className="text-center py-8">
              <div className="text-4xl mb-4">➕</div>
              <p className="text-gray-600">No courses created yet</p>
              <button className="mt-4 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700">
                Add Course
              </button>
            </div>
          </div>

          {/* User Management */}
          <div className="bg-white rounded-lg shadow-md p-6">
            <h2 className="text-xl font-bold text-gray-900 mb-4">
              👥 User Management
            </h2>
            <div className="text-center py-8">
              <div className="text-4xl mb-4">👤</div>
              <p className="text-gray-600">No users to display</p>
              <button className="mt-4 bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700">
                View Users
              </button>
            </div>
          </div>

          {/* Enrollment Management */}
          <div className="bg-white rounded-lg shadow-md p-6">
            <h2 className="text-xl font-bold text-gray-900 mb-4">
              📈 Enrollment Management
            </h2>
            <div className="text-center py-8">
              <div className="text-4xl mb-4">📊</div>
              <p className="text-gray-600">No enrollments to track</p>
              <button className="mt-4 bg-orange-600 text-white px-4 py-2 rounded-lg hover:bg-orange-700">
                View Enrollments
              </button>
            </div>
          </div>
        </div>

        {/* Navigation */}
        <div className="mt-8 text-center">
          <div className="bg-white rounded-lg shadow-md p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Navigation</h3>
            <div className="flex flex-wrap justify-center gap-4">
              <a 
                href="/" 
                className="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700"
              >
                🏠 Home
              </a>
              <a 
                href="/dashboard" 
                className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700"
              >
                📊 User Dashboard
              </a>
              <a 
                href="/admin" 
                className="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700"
              >
                🛡️ Full Admin Panel
              </a>
              <a 
                href="/admin-test" 
                className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700"
              >
                🔧 Admin Test
              </a>
            </div>
          </div>
        </div>

        {/* Debug Info */}
        <div className="mt-8">
          <div className="bg-gray-100 rounded-lg p-4">
            <h4 className="font-semibold text-gray-900 mb-2">Debug Information</h4>
            <div className="text-sm text-gray-600 space-y-1">
              <p><strong>User ID:</strong> {user.id}</p>
              <p><strong>Email:</strong> {user.email}</p>
              <p><strong>Current Time:</strong> {new Date().toLocaleString()}</p>
              <p><strong>Admin Panel Status:</strong> ✅ Simple Admin Panel Working</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SimpleAdmin;
