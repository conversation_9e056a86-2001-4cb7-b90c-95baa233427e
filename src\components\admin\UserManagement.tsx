import React, { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { Search, Users, Mail, Calendar, MapPin } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

interface User {
  id: string;
  full_name: string | null;
  email: string | null;
  country: string | null;
  created_at: string | null;
  enrollments?: {
    id: string;
    course_id: string;
    progress: number | null;
    completed_at: string | null;
    courses: {
      title: string;
    };
  }[];
}

const UserManagement = () => {
  const { toast } = useToast();
  const [users, setUsers] = useState<User[]>([]);
  const [filteredUsers, setFilteredUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');

  useEffect(() => {
    fetchUsers();
  }, []);

  useEffect(() => {
    // Filter users based on search term
    const filtered = users.filter(user => 
      user.full_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.email?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.country?.toLowerCase().includes(searchTerm.toLowerCase())
    );
    setFilteredUsers(filtered);
  }, [users, searchTerm]);

  const fetchUsers = async () => {
    try {
      const { data, error } = await supabase
        .from('profiles')
        .select(`
          *,
          enrollments (
            id,
            course_id,
            progress,
            completed_at,
            courses (title)
          )
        `)
        .order('created_at', { ascending: false });

      if (error) throw error;
      setUsers(data || []);
    } catch (error) {
      console.error('Error fetching users:', error);
      toast({
        title: "Error",
        description: "Failed to load users",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const getInitials = (name: string | null) => {
    if (!name) return 'U';
    return name.split(' ').map(n => n[0]).join('').toUpperCase().slice(0, 2);
  };

  const getEnrollmentStats = (user: User) => {
    const enrollments = user.enrollments || [];
    const completed = enrollments.filter(e => e.completed_at).length;
    const inProgress = enrollments.length - completed;
    const avgProgress = enrollments.length > 0 
      ? Math.round(enrollments.reduce((sum, e) => sum + (e.progress || 0), 0) / enrollments.length)
      : 0;

    return { total: enrollments.length, completed, inProgress, avgProgress };
  };

  // Show loading state without early return
  if (loading) {
    return (
      <div className="flex justify-center items-center py-12">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-green-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h3 className="text-lg font-semibold">User Management</h3>
          <p className="text-gray-600">View and manage all registered users</p>
        </div>
        <div className="flex items-center gap-4">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <Input
              placeholder="Search users..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 w-64"
            />
          </div>
          <Badge variant="secondary" className="text-sm">
            {filteredUsers.length} users
          </Badge>
        </div>
      </div>

      {filteredUsers.length === 0 ? (
        <div className="text-center py-12">
          <Users className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-gray-900 mb-2">
            {searchTerm ? 'No users found' : 'No users yet'}
          </h3>
          <p className="text-gray-600">
            {searchTerm ? 'Try adjusting your search terms' : 'Users will appear here once they register'}
          </p>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredUsers.map((user) => {
            const stats = getEnrollmentStats(user);
            return (
              <Card key={user.id} className="hover:shadow-lg transition-shadow">
                <CardContent className="p-6">
                  <div className="flex items-start gap-4 mb-4">
                    <Avatar className="h-12 w-12">
                      <AvatarFallback className="bg-green-100 text-green-700 font-semibold">
                        {getInitials(user.full_name)}
                      </AvatarFallback>
                    </Avatar>
                    <div className="flex-1 min-w-0">
                      <h3 className="text-lg font-semibold text-gray-900 truncate">
                        {user.full_name || 'Unknown User'}
                      </h3>
                      <div className="flex items-center gap-1 text-sm text-gray-600 mb-1">
                        <Mail className="h-3 w-3" />
                        <span className="truncate">{user.email || 'No email'}</span>
                      </div>
                      {user.country && (
                        <div className="flex items-center gap-1 text-sm text-gray-600">
                          <MapPin className="h-3 w-3" />
                          <span>{user.country}</span>
                        </div>
                      )}
                    </div>
                  </div>

                  <div className="space-y-3">
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-gray-600">Joined</span>
                      <span className="font-medium">
                        {user.created_at ? new Date(user.created_at).toLocaleDateString() : 'Unknown'}
                      </span>
                    </div>

                    <div className="grid grid-cols-3 gap-2 text-center">
                      <div className="bg-blue-50 rounded-lg p-2">
                        <div className="text-lg font-bold text-blue-600">{stats.total}</div>
                        <div className="text-xs text-blue-600">Enrolled</div>
                      </div>
                      <div className="bg-green-50 rounded-lg p-2">
                        <div className="text-lg font-bold text-green-600">{stats.completed}</div>
                        <div className="text-xs text-green-600">Completed</div>
                      </div>
                      <div className="bg-orange-50 rounded-lg p-2">
                        <div className="text-lg font-bold text-orange-600">{stats.avgProgress}%</div>
                        <div className="text-xs text-orange-600">Avg Progress</div>
                      </div>
                    </div>

                    {user.enrollments && user.enrollments.length > 0 && (
                      <div>
                        <h4 className="text-sm font-medium text-gray-900 mb-2">Current Courses</h4>
                        <div className="space-y-1">
                          {user.enrollments.slice(0, 3).map((enrollment) => (
                            <div key={enrollment.id} className="flex items-center justify-between text-xs">
                              <span className="text-gray-600 truncate flex-1">
                                {enrollment.courses.title}
                              </span>
                              <Badge 
                                variant={enrollment.completed_at ? "default" : "secondary"}
                                className="ml-2 text-xs"
                              >
                                {enrollment.completed_at ? 'Done' : `${enrollment.progress || 0}%`}
                              </Badge>
                            </div>
                          ))}
                          {user.enrollments.length > 3 && (
                            <div className="text-xs text-gray-500 text-center pt-1">
                              +{user.enrollments.length - 3} more courses
                            </div>
                          )}
                        </div>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            );
          })}
        </div>
      )}
    </div>
  );
};

export default UserManagement;
